# Steam CDN登录接口爆破工具 - 使用说明

## 功能特点

- 🖥️ **图形化界面**: 友好的GUI界面，操作简单直观
- ⚡ **异步请求**: 高效的异步HTTP请求处理
- 🔄 **断点续传**: 支持中断后从上次位置继续
- 🌐 **代理支持**: 支持HTTP代理设置
- 📊 **实时日志**: 实时显示运行状态和结果
- 🎯 **智能过滤**: 自动过滤无效账号
- 💾 **结果保存**: 自动保存成功的账号信息

## 安装和运行

### 方式一：直接运行Python脚本

1. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```

2. **运行程序**:
   ```bash
   python Steam爆破工具.py
   ```

3. **控制台模式**（可选）:
   ```bash
   python Steam爆破工具.py --console
   ```

### 方式二：使用打包的exe文件

1. **打包程序**:
   ```bash
   python 打包脚本.py
   ```

2. **运行exe文件**:
   - 打包完成后，在 `dist` 目录中找到 `Steam爆破工具.exe`
   - 双击运行即可，无需安装Python环境

## 使用步骤

### 1. 准备字典文件
- 创建一个文本文件（如 `dict.txt`）
- 每行一个账号，程序会使用账号作为密码进行尝试
- 账号长度必须≥6位才会被处理

### 2. 配置参数
- **目标URL**: Steam CDN登录接口地址（默认已设置）
- **字典文件**: 选择包含账号列表的文本文件
- **代理地址**: 设置HTTP代理（可选）
- **请求间隔**: 设置请求之间的延迟时间（秒）

### 3. 开始爆破
- 点击"开始爆破"按钮
- 程序会显示实时进度和日志信息
- 成功的账号会特别标记并保存到文件

### 4. 查看结果
- **实时日志**: 在GUI界面中查看运行状态
- **结果日志.log**: 详细的请求记录
- **成功账号.txt**: 可能成功的账号列表
- **爆破日志.log**: 完整的运行日志

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `Steam爆破工具.py` | 主程序文件（GUI版本） |
| `打包脚本.py` | 用于打包成exe的脚本 |
| `requirements.txt` | Python依赖包列表 |
| `dict.txt` | 字典文件（需要自己创建） |
| `进度记录.txt` | 断点续传进度记录 |
| `结果日志.log` | 详细的请求结果记录 |
| `成功账号.txt` | 可能成功的账号列表 |
| `爆破日志.log` | 完整的程序运行日志 |

## 注意事项

⚠️ **重要提醒**:
- 本工具仅供学习和安全测试使用
- 请勿用于非法用途
- 使用前请确保有相关授权
- 遵守当地法律法规

🔧 **技术提示**:
- 建议设置合理的请求间隔，避免过于频繁的请求
- 如果网络不稳定，可以适当增加超时时间
- 使用代理可以提高请求成功率
- 程序支持中断后继续，不会丢失进度

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本（建议3.7+）
   - 确认已安装所有依赖包

2. **字典文件读取失败**
   - 检查文件路径是否正确
   - 确认文件编码为UTF-8

3. **网络请求失败**
   - 检查网络连接
   - 验证代理设置
   - 确认目标URL可访问

4. **打包失败**
   - 确认已安装PyInstaller
   - 检查Python环境是否完整
   - 查看错误日志定位问题

### 获取帮助

如果遇到问题，可以：
1. 查看程序日志文件
2. 检查配置参数是否正确
3. 尝试重新安装依赖包
4. 使用控制台模式查看详细错误信息

## 更新日志

### v1.0.0
- 初始版本发布
- 支持GUI界面
- 实现基本爆破功能
- 添加断点续传
- 支持代理设置
