@echo off
chcp 65001 >nul
title Steam爆破工具启动器

echo ========================================
echo Steam CDN登录接口爆破工具
echo ========================================
echo.

echo 选择运行模式:
echo 1. GUI界面模式 (推荐)
echo 2. 控制台模式
echo 3. 打包成exe文件
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo.
    echo 启动GUI模式...
    python Steam爆破工具.py
) else if "%choice%"=="2" (
    echo.
    echo 启动控制台模式...
    python Steam爆破工具.py --console
) else if "%choice%"=="3" (
    echo.
    echo 开始打包程序...
    python 打包脚本.py
) else if "%choice%"=="4" (
    echo.
    echo 再见!
    exit /b 0
) else (
    echo.
    echo 无效选择，请重新运行脚本
)

echo.
pause
