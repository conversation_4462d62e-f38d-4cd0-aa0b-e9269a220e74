#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Steam爆破工具打包脚本
使用PyInstaller将Python程序打包成独立的exe文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path


class PackageBuilder:
    def __init__(self):
        self.script_name = "Steam爆破工具.py"
        self.exe_name = "Steam爆破工具"
        self.dist_dir = "dist"
        self.build_dir = "build"
        self.spec_file = f"{self.exe_name}.spec"
        
    def check_requirements(self):
        """检查打包环境"""
        print("检查打包环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        if python_version < (3, 7):
            print("❌ Python版本过低，建议使用Python 3.7+")
            return False
            
        # 检查源文件
        if not Path(self.script_name).exists():
            print(f"❌ 源文件 {self.script_name} 不存在")
            return False
            
        # 检查PyInstaller
        try:
            result = subprocess.run(["pyinstaller", "--version"], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ PyInstaller版本: {result.stdout.strip()}")
            else:
                print("❌ PyInstaller未安装或版本有问题")
                return False
        except FileNotFoundError:
            print("❌ PyInstaller未安装")
            print("请运行: pip install pyinstaller")
            return False
            
        return True
        
    def install_dependencies(self):
        """安装依赖"""
        print("\n安装依赖包...")
        
        if Path("requirements.txt").exists():
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                             check=True)
                print("✅ 依赖安装完成")
                return True
            except subprocess.CalledProcessError:
                print("❌ 依赖安装失败")
                return False
        else:
            print("⚠️ requirements.txt文件不存在，跳过依赖安装")
            return True
            
    def clean_build_files(self):
        """清理构建文件"""
        print("\n清理旧的构建文件...")
        
        dirs_to_clean = [self.dist_dir, self.build_dir]
        files_to_clean = [self.spec_file]
        
        for dir_path in dirs_to_clean:
            if Path(dir_path).exists():
                shutil.rmtree(dir_path)
                print(f"✅ 删除目录: {dir_path}")
                
        for file_path in files_to_clean:
            if Path(file_path).exists():
                os.remove(file_path)
                print(f"✅ 删除文件: {file_path}")
                
    def create_spec_file(self):
        """创建spec文件"""
        print("\n创建PyInstaller配置文件...")
        
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{self.script_name}'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'asyncio',
        'aiohttp',
        'tkinter',
        'tkinter.ttk',
        'tkinter.filedialog',
        'tkinter.messagebox',
        'tkinter.scrolledtext',
        'threading',
        'queue',
        'logging',
        'json',
        'pathlib'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.exe_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 设置为False隐藏控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,  # 可以在这里指定图标文件
)
'''
        
        with open(self.spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
            
        print(f"✅ 创建配置文件: {self.spec_file}")
        
    def build_executable(self):
        """构建可执行文件"""
        print("\n开始构建可执行文件...")
        print("这可能需要几分钟时间，请耐心等待...")
        
        try:
            # 使用spec文件构建
            cmd = ["pyinstaller", "--clean", self.spec_file]
            
            print(f"执行命令: {' '.join(cmd)}")
            result = subprocess.run(cmd, check=True, capture_output=True, text=True)
            
            print("✅ 构建完成")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ 构建失败: {e}")
            print(f"错误输出: {e.stderr}")
            return False
            
    def verify_build(self):
        """验证构建结果"""
        print("\n验证构建结果...")
        
        exe_path = Path(self.dist_dir) / f"{self.exe_name}.exe"
        
        if exe_path.exists():
            file_size = exe_path.stat().st_size / (1024 * 1024)  # MB
            print(f"✅ 可执行文件已生成: {exe_path}")
            print(f"✅ 文件大小: {file_size:.2f} MB")
            
            # 检查是否有其他必要文件
            dist_files = list(Path(self.dist_dir).iterdir())
            print(f"✅ 输出目录包含 {len(dist_files)} 个文件/目录")
            
            return True
        else:
            print(f"❌ 可执行文件未找到: {exe_path}")
            return False
            
    def create_batch_file(self):
        """创建批处理文件用于快速启动"""
        print("\n创建启动脚本...")
        
        batch_content = f'''@echo off
cd /d "%~dp0"
start "" "{self.exe_name}.exe"
'''
        
        batch_file = f"启动{self.exe_name}.bat"
        with open(batch_file, 'w', encoding='gbk') as f:
            f.write(batch_content)
            
        print(f"✅ 创建启动脚本: {batch_file}")
        
    def package(self):
        """执行完整的打包流程"""
        print("=" * 60)
        print("Steam爆破工具打包程序")
        print("=" * 60)
        
        steps = [
            ("检查环境", self.check_requirements),
            ("安装依赖", self.install_dependencies),
            ("清理文件", self.clean_build_files),
            ("创建配置", self.create_spec_file),
            ("构建程序", self.build_executable),
            ("验证结果", self.verify_build),
            ("创建脚本", self.create_batch_file),
        ]
        
        for step_name, step_func in steps:
            print(f"\n{'='*20} {step_name} {'='*20}")
            if not step_func():
                print(f"\n❌ {step_name}失败，打包中止")
                return False
                
        print("\n" + "=" * 60)
        print("🎉 打包完成！")
        print(f"📁 可执行文件位置: {Path(self.dist_dir).absolute()}")
        print(f"🚀 运行程序: {Path(self.dist_dir) / f'{self.exe_name}.exe'}")
        print("=" * 60)
        
        return True


def main():
    """主函数"""
    builder = PackageBuilder()
    
    try:
        success = builder.package()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n打包失败，按回车键退出...")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n\n用户中断打包过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n打包过程中发生未知错误: {str(e)}")
        input("按回车键退出...")
        sys.exit(1)


if __name__ == "__main__":
    main()
